'use client'

import { Skeleton } from '@/components/common'

const ProductSkeleton = () => {
  return (
    <div className="max-container relative min-h-screen">
      {/* 面包屑骨架 */}
      <div className="mb-base-12">
        <div className="flex items-center gap-2">
          <Skeleton style={{ width: 60, height: 16, borderRadius: 4 }} />
          <span className="text-gray-400">/</span>
          <Skeleton style={{ width: 80, height: 16, borderRadius: 4 }} />
          <span className="text-gray-400">/</span>
          <Skeleton style={{ width: 120, height: 16, borderRadius: 4 }} />
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex gap-16 pb-16">
        {/* 左侧内容区域 - 9/14比例 */}
        <div className="min-w-0 flex-[9]">
          {/* 商品图片展示骨架 */}
          <div className="mb-16">
            <Skeleton style={{ width: '100%', height: 600, borderRadius: 12 }} />
          </div>

          {/* 商品详情切换区域骨架 */}
          <div className="mb-16">
            {/* Tab Header 骨架 */}
            <div className="mb-6 flex gap-8">
              <Skeleton style={{ width: 80, height: 32, borderRadius: 6 }} />
              <Skeleton style={{ width: 60, height: 32, borderRadius: 6 }} />
              <Skeleton style={{ width: 100, height: 32, borderRadius: 6 }} />
              <Skeleton style={{ width: 90, height: 32, borderRadius: 6 }} />
            </div>

            {/* Tab Content 骨架 */}
            <div className="space-y-4">
              <Skeleton style={{ width: '100%', height: 200, borderRadius: 8 }} />
              <Skeleton style={{ width: '80%', height: 100, borderRadius: 8 }} />
              <Skeleton style={{ width: '90%', height: 150, borderRadius: 8 }} />
            </div>
          </div>
        </div>

        {/* 右侧固定信息区域 - 5/14比例 */}
        <div className="min-w-[434px] flex-[5]">
          <div className="sticky top-[92px] bg-white">
            {/* 产品基本信息骨架 */}
            <div className="mb-6 space-y-4">
              {/* 产品标题 */}
              <Skeleton style={{ width: '90%', height: 28, borderRadius: 6 }} />
              <Skeleton style={{ width: '70%', height: 20, borderRadius: 4 }} />

              {/* 价格信息 */}
              <div className="flex items-center gap-4">
                <Skeleton style={{ width: 120, height: 32, borderRadius: 6 }} />
                <Skeleton style={{ width: 80, height: 20, borderRadius: 4 }} />
              </div>
            </div>

            {/* 产品选项骨架 */}
            <div className="mb-6 space-y-6">
              {/* 颜色选择 */}
              <div>
                <Skeleton style={{ width: 60, height: 16, borderRadius: 4, marginBottom: 12 }} />
                <div className="flex gap-3">
                  <Skeleton style={{ width: 40, height: 40, borderRadius: 8 }} />
                  <Skeleton style={{ width: 40, height: 40, borderRadius: 8 }} />
                  <Skeleton style={{ width: 40, height: 40, borderRadius: 8 }} />
                </div>
              </div>

              {/* 尺寸选择 */}
              <div>
                <Skeleton style={{ width: 40, height: 16, borderRadius: 4, marginBottom: 12 }} />
                <div className="flex gap-3">
                  <Skeleton style={{ width: 60, height: 36, borderRadius: 6 }} />
                  <Skeleton style={{ width: 60, height: 36, borderRadius: 6 }} />
                  <Skeleton style={{ width: 60, height: 36, borderRadius: 6 }} />
                </div>
              </div>

              {/* 数量选择 */}
              <div>
                <Skeleton style={{ width: 40, height: 16, borderRadius: 4, marginBottom: 12 }} />
                <Skeleton style={{ width: 120, height: 36, borderRadius: 6 }} />
              </div>
            </div>

            {/* 服务信息骨架 */}
            <div className="mb-6 space-y-3">
              <Skeleton style={{ width: '100%', height: 40, borderRadius: 8 }} />
              <Skeleton style={{ width: '100%', height: 40, borderRadius: 8 }} />
              <Skeleton style={{ width: '100%', height: 40, borderRadius: 8 }} />
            </div>

            {/* 优惠券骨架 */}
            <div className="mb-6">
              <Skeleton style={{ width: '100%', height: 60, borderRadius: 8 }} />
            </div>

            {/* 操作按钮骨架 */}
            <div className="flex gap-4">
              <Skeleton style={{ width: 60, height: 48, borderRadius: 8 }} />
              <Skeleton style={{ width: 60, height: 48, borderRadius: 8 }} />
              <Skeleton style={{ flex: 1, height: 48, borderRadius: 24 }} />
            </div>
          </div>
        </div>
      </div>

      {/* 推荐商品骨架 */}
      <div className="mt-32">
        <Skeleton style={{ width: 120, height: 28, borderRadius: 6, marginBottom: 24 }} />
        <div className="grid grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="space-y-3">
              <Skeleton style={{ width: '100%', aspectRatio: '1/1', borderRadius: 12 }} />
              <Skeleton style={{ width: '80%', height: 16, borderRadius: 4 }} />
              <Skeleton style={{ width: '60%', height: 14, borderRadius: 4 }} />
              <Skeleton style={{ width: '70%', height: 18, borderRadius: 4 }} />
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default ProductSkeleton
